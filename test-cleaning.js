/**
 * CSV Processing Test Script - Tests Production API Routes
 * Run with: node test-cleaning.js
 *
 * This script uploads a CSV file using the actual production API routes
 * and then calculates GMV from the processed data in Supabase.
 */

import fs from 'fs';
import path from 'path';

// CSV file path
const CSV_FILE_PATH = '/Users/<USER>/Documents/GitHub/phia/phia interior dash/csv-1750774482563.csv';

// API base URL (assuming local development)
const API_BASE_URL = 'http://localhost:3000';

/**
 * Upload CSV file using the production API
 */
async function uploadCSVFile() {
  try {
    console.log('📁 Reading CSV file...');
    const fileBuffer = fs.readFileSync(CSV_FILE_PATH);
    const fileName = path.basename(CSV_FILE_PATH);

    // Create FormData for file upload
    const formData = new FormData();
    const file = new File([fileBuffer], fileName, { type: 'text/csv' });
    formData.append('file', file);

    console.log('⬆️  Uploading CSV file to production API...');
    const uploadResponse = await fetch(`${API_BASE_URL}/api/upload`, {
      method: 'POST',
      body: formData
    });

    if (!uploadResponse.ok) {
      throw new Error(`Upload failed: ${uploadResponse.statusText}`);
    }

    const uploadResult = await uploadResponse.json();
    console.log('✅ Upload successful:', uploadResult);

    return uploadResult.uploadId;
  } catch (error) {
    console.error('❌ Upload failed:', error.message);
    throw error;
  }
}

/**
 * Process the uploaded CSV using the production API
 */
async function processCSVFile(uploadId) {
  try {
    console.log('\n🔄 Processing CSV file...');
    let isComplete = false;
    let startRow = 0;
    let totalStats = {
      processedRows: 0,
      successfulRows: 0,
      errorRows: 0,
      filteredRows: 0
    };

    while (!isComplete) {
      console.log(`   Processing from row ${startRow}...`);

      const processResponse = await fetch(`${API_BASE_URL}/api/upload/process`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          uploadId,
          startRow
        })
      });

      if (!processResponse.ok) {
        throw new Error(`Processing failed: ${processResponse.statusText}`);
      }

      const result = await processResponse.json();

      totalStats.processedRows = result.processedRows;
      totalStats.successfulRows = result.successfulRows;
      totalStats.errorRows = result.errorRows;
      totalStats.filteredRows = result.filteredRows || 0;

      isComplete = result.isComplete;
      startRow = result.nextStartRow;

      console.log(`   Progress: ${result.progress}% (${result.processedRows}/${result.totalRows})`);
    }

    console.log('✅ Processing complete!');
    console.log('📊 Final Stats:', totalStats);

    return totalStats;
  } catch (error) {
    console.error('❌ Processing failed:', error.message);
    throw error;
  }
}

function processCSVFile() {
  try {
    // Read the CSV file
    const csvContent = fs.readFileSync(CSV_FILE_PATH, 'utf8');
    const lines = csvContent.split('\n').filter(line => line.trim());

    if (lines.length < 2) {
      console.error('CSV file must contain at least a header row and one data row');
      return;
    }

    // Parse header
    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
    console.log('CSV Headers:', headers);

    // Find column indices
    const dateIndex = headers.findIndex(h => h.toLowerCase().includes('date'));
    const merchantIndex = headers.findIndex(h => h.toLowerCase().includes('merchant'));
    const orderAmountIndex = headers.findIndex(h => h.toLowerCase().includes('order amount'));
    const commissionIndex = headers.findIndex(h => h.toLowerCase().includes('commission'));
    const statusIndex = headers.findIndex(h => h.toLowerCase().includes('status'));

    console.log('\nColumn Mapping:');
    console.log(`Date: ${dateIndex} (${headers[dateIndex]})`);
    console.log(`Merchant: ${merchantIndex} (${headers[merchantIndex]})`);
    console.log(`Order Amount: ${orderAmountIndex} (${headers[orderAmountIndex]})`);
    console.log(`Commission: ${commissionIndex} (${headers[commissionIndex]})`);
    console.log(`Status: ${statusIndex} (${headers[statusIndex]})`);

    // Process data rows
    const dataRows = lines.slice(1);
    const dailyGMV = {};
    let totalProcessed = 0;
    let totalFiltered = 0;
    let totalValid = 0;

    // Detailed filtering reasons
    const filterReasons = {
      'Invalid date': 0,
      'Outside date range': 0,
      'Zero order amount': 0,
      'Negative order amount (return/refund)': 0,
      'Missing merchant name': 0,
      'Insufficient columns': 0
    };

    // Get date range for the past week (or remove this to process all dates)
    const today = new Date();
    const oneWeekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

    console.log(`\nProcessing ${dataRows.length} transactions...`);
    console.log(`Date range filter: ${oneWeekAgo.toDateString()} to ${today.toDateString()}`);

    dataRows.forEach((row, index) => {
      totalProcessed++;
      const columns = row.split(',').map(col => col.trim().replace(/"/g, ''));

      if (columns.length < Math.max(dateIndex, merchantIndex, orderAmountIndex, commissionIndex, statusIndex) + 1) {
        filterReasons['Insufficient columns']++;
        return;
      }

      const dateStr = columns[dateIndex] || '';
      const merchant = columns[merchantIndex] || '';
      const orderAmountStr = columns[orderAmountIndex] || '';
      const commissionStr = columns[commissionIndex] || '';
      const status = columns[statusIndex] || '';

      // Parse date
      const transactionDate = parseDate(dateStr);
      if (!transactionDate) {
        filterReasons['Invalid date']++;
        return;
      }

      // Filter by date range (past week)
      if (transactionDate < oneWeekAgo || transactionDate > today) {
        filterReasons['Outside date range']++;
        return; // Skip transactions outside the past week
      }

      // Parse amounts
      const orderAmount = parseAmount(orderAmountStr, true);
      const commissionAmount = parseAmount(commissionStr, true);

      // Normalize merchant name
      const normalizedMerchant = DATA_CLEANING_CONFIG.NORMALIZE_MERCHANT_NAMES
        ? normalizeMerchantName(merchant)
        : merchant;

      // Check if transaction should be filtered
      const filterCheck = shouldFilterTransaction(orderAmount, commissionAmount, normalizedMerchant);
      if (filterCheck.shouldFilter) {
        filterReasons[filterCheck.reason] = (filterReasons[filterCheck.reason] || 0) + 1;
        totalFiltered++;
        return;
      }

      // Valid transaction - add to daily GMV
      totalValid++;
      const dateKey = transactionDate.toISOString().split('T')[0]; // YYYY-MM-DD format

      if (!dailyGMV[dateKey]) {
        dailyGMV[dateKey] = {
          date: dateKey,
          transactionCount: 0,
          totalGMV: 0,
          totalCommission: 0,
          transactions: []
        };
      }

      dailyGMV[dateKey].transactionCount++;
      dailyGMV[dateKey].totalGMV += Math.abs(orderAmount);
      dailyGMV[dateKey].totalCommission += Math.abs(commissionAmount);
      dailyGMV[dateKey].transactions.push({
        merchant: normalizedMerchant,
        amount: Math.abs(orderAmount),
        commission: Math.abs(commissionAmount),
        isReturn: orderAmount < 0
      });
    });

    // Display results
    console.log('\n' + '='.repeat(80));
    console.log('PROCESSING SUMMARY');
    console.log('='.repeat(80));
    console.log(`Total rows processed: ${totalProcessed}`);
    console.log(`Valid transactions: ${totalValid}`);
    console.log(`Filtered transactions: ${totalFiltered}`);
    console.log(`Duplicate transactions: ${totalDuplicates}`);

    const totalAccountedFor = totalValid + totalFiltered + totalDuplicates;
    const unaccountedFor = totalProcessed - totalAccountedFor;
    if (unaccountedFor > 0) {
      console.log(`⚠️  Unaccounted transactions: ${unaccountedFor}`);
    }

    console.log('\n📊 DETAILED FILTERING BREAKDOWN:');
    console.log('-'.repeat(50));
    Object.entries(filterReasons).forEach(([reason, count]) => {
      if (count > 0) {
        console.log(`   ${reason}: ${count}`);
      }
    });

    // Show percentage breakdown
    console.log('\n📈 PERCENTAGE BREAKDOWN:');
    console.log('-'.repeat(50));
    console.log(`   Valid: ${((totalValid / totalProcessed) * 100).toFixed(1)}%`);
    console.log(`   Filtered: ${((totalFiltered / totalProcessed) * 100).toFixed(1)}%`);
    console.log(`   Duplicates: ${((totalDuplicates / totalProcessed) * 100).toFixed(1)}%`);
    if (unaccountedFor > 0) {
      console.log(`   Unaccounted: ${((unaccountedFor / totalProcessed) * 100).toFixed(1)}%`);
    }

    console.log('\n' + '='.repeat(80));
    console.log('DAILY GMV BREAKDOWN (Past 7 Days)');
    console.log('='.repeat(80));

    // Sort dates and display
    const sortedDates = Object.keys(dailyGMV).sort();
    let totalWeekGMV = 0;
    let totalWeekCommission = 0;
    let totalWeekTransactions = 0;

    if (sortedDates.length === 0) {
      console.log('No transactions found in the past 7 days.');
    } else {
      sortedDates.forEach(dateKey => {
        const dayData = dailyGMV[dateKey];
        const dayOfWeek = new Date(dateKey).toLocaleDateString('en-US', { weekday: 'long' });

        console.log(`\n📅 ${dateKey} (${dayOfWeek})`);
        console.log(`   Transactions: ${dayData.transactionCount}`);
        console.log(`   GMV: $${dayData.totalGMV.toFixed(2)}`);
        console.log(`   Commission: $${dayData.totalCommission.toFixed(2)}`);
        console.log(`   Avg Order Value: $${(dayData.totalGMV / dayData.transactionCount).toFixed(2)}`);

        totalWeekGMV += dayData.totalGMV;
        totalWeekCommission += dayData.totalCommission;
        totalWeekTransactions += dayData.transactionCount;
      });

      console.log('\n' + '='.repeat(80));
      console.log('WEEKLY TOTALS');
      console.log('='.repeat(80));
      console.log(`Total Transactions: ${totalWeekTransactions}`);
      console.log(`Total GMV: $${totalWeekGMV.toFixed(2)}`);
      console.log(`Total Commission: $${totalWeekCommission.toFixed(2)}`);
      console.log(`Average Daily GMV: $${(totalWeekGMV / Math.max(sortedDates.length, 1)).toFixed(2)}`);
      console.log(`Average Order Value: $${(totalWeekGMV / Math.max(totalWeekTransactions, 1)).toFixed(2)}`);
      console.log(`Commission Rate: ${((totalWeekCommission / totalWeekGMV) * 100).toFixed(2)}%`);
    }

  } catch (error) {
    console.error('Error processing CSV file:', error.message);
  }
}

// Run the processing
console.log('CSV Processing Test with GMV Calculation');
console.log('==========================================\n');
processCSVFile();
